from smolagents import Tool

from agent_review.retrieval_core import RetrievalCore


class RetrieverTool(Tool):
    name = "retriever"
    description = "Uses semantic search to retrieve relevant parts of user conversation history that could help answer your query."
    inputs = {
        "query": {
            "type": "string",
            "description": "The query to search conversation history. This should be semantically close to the information you're looking for in past conversations. Use the affirmative form rather than a question.",
        }
    }
    output_type = "string"

    def __init__(self, knowledge_base: RetrievalCore):
        super().__init__()
        self.knowledge_base = knowledge_base

    @staticmethod
    def get_detailed_instruct(task_description: str, query: str) -> str:
        return f"Instruct: {task_description}\nQuery:{query}"

    def forward(self, query: str) -> str:
        """Execute the retrieval based on the provided query."""
        assert isinstance(query, str), "Your search query must be a string"

        # Each query must come with a one-sentence instruction that describes the task
        task = (
            "Given a customer service conversation query, retrieve relevant messages that contain sensitive content or compliance risks"
        )
        query = self.get_detailed_instruct(task, query)

        # Retrieve relevant documents
        docs = self.knowledge_base.search(query)

        # Format the retrieved documents for readability
        return "\nRetrieved documents:\n" + "".join(
            [
                f"\n\n===== Document {str(i)} =====\n"
                + f"Content: {doc.content}\n"
                + f"Metadata: {doc.meta}\n"
                + f"Score: {doc.score}\n"
                for i, doc in enumerate(docs)
            ]
        )


# retriever_tool = RetrieverTool()
