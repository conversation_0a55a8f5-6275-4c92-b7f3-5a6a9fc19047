import os

import httpx
from agno.agent import Agent
from agno.embedder.openai import Open<PERSON>IEmbedder
from agno.knowledge.pdf_url import PDFUrlKnowledgeBase
from agno.models.openai.like import OpenAILike
from agno.vectordb.mongodb import MongoDb
from dotenv import load_dotenv
from openai import OpenAI

load_dotenv(override=True)

mdb_connection_string = "mongodb://localhost:27017/?directConnection=true"

knowledge_base = PDFUrlKnowledgeBase(
    urls=["https://agno-public.s3.amazonaws.com/recipes/ThaiRecipes.pdf"],
    vector_db=MongoDb(
        collection_name="recipes",
        db_url=mdb_connection_string,
        embedder=OpenAIEmbedder(
            openai_client=OpenAI(
                base_url=os.environ["EMBEDDING_BASE_URL"],
                api_key=os.environ["EMBEDDING_API_KEY"],
                http_client=httpx.Client(verify=False, timeout=30.0),
            ),
            id=os.environ["EMBEDDING_MODEL"],
            dimensions=os.environ["EMBEDDING_DIMS"],
        ),
    ),
)  # adjust wait_after_insert and wait_until_index_ready to your needs

# knowledge_base.load(recreate=True)  # Comment out after first run

agent = Agent(
    model=OpenAILike(
        id=os.environ["MODEL_NAME"],
        api_key=os.environ["QWQ_API_KEY"],
        base_url=os.environ["QWQ_BASE_URL"],
        http_client=httpx.Client(verify=False, timeout=60.0),
    ),
    knowledge=knowledge_base,
    show_tool_calls=True,
)
agent.print_response("How to make Thai curry?", markdown=True)
