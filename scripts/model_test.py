import os
import time
from enum import Enum

import httpx

# import tiktoken
from dotenv import load_dotenv
from openai import OpenAI
from pydantic import BaseModel

load_dotenv(override=True)


class CarType(str, Enum):
    sedan = "sedan"
    suv = "SUV"
    truck = "Truck"
    coupe = "Coupe"


class CarDescription(BaseModel):
    brand: str
    model: str
    car_type: CarType


json_schema = CarDescription.model_json_schema()
# print(json_schema)

client = OpenAI(
    base_url=os.environ["QWQ_BASE_URL"],
    api_key=os.environ["QWQ_API_KEY"],
    http_client=httpx.Client(
        verify=False, timeout=60.0
    ),  # Disable SSL verification and increase timeout
)
st = time.time()

print(f"Connecting to: {os.environ['QWQ_BASE_URL']}")
print(f"Using model: {os.environ['MODEL_NAME']}")

try:
    chat_completion = client.chat.completions.create(
        # messages=[
        #     {
        #         "role": "user",
        #         "content": "Generate a JSON with the brand, model and car_type of the most iconic car from the 90's",
        #     }
        # ],
        messages=[
            {
                "role": "user",
                "content": "I have 1 million yuan and want to buy the most classic car from the 90s. Please give me a JSON format recommendation including brand, model and type.",
                # "content": "What's your name?",
            },
            {"role": "user", "content": "Please reply in English"},
        ],
        model=os.environ["MODEL_NAME"],
        temperature=0,
        # stream=True,
        response_format={
            "type": "json_schema",
            "json_schema": {
                "name": "result",
                # convert the pydantic model to json schema
                "schema": json_schema,
            },
        },
        extra_body={
            # "guided_json": json_schema,
            "chat_template_kwargs": {"enable_thinking": False},
        },
    )
    res = chat_completion.choices[0].message.content
    # print(chat_completion)
    print(res)
    if res is None:
        print("No response content")
    # print(type(res))
    # print("Response content:")
    # print(chat_completion.choices[0].message.reasoning_content)
    print(f"请求耗时: {time.time() - st:.2f}秒")

except Exception as e:
    print(f"请求失败: {type(e).__name__}: {e}")
    print(f"错误详情: {str(e)}")
    if hasattr(e, "response"):
        print(
            f"响应状态码: {e.response.status_code if hasattr(e.response, 'status_code') else 'N/A'}"
        )
        print(f"响应内容: {e.response.text if hasattr(e.response, 'text') else 'N/A'}")

# reasoning_content = chat_completion.choices[0].message.reasoning_content

# print("Reasoning content:")
# print(reasoning_content)
# print("Response content:")
# print(res)
# print(time.time() - st)


# 添加一个本地计算token的函数
# def count_tokens_locally(text, model_name='gpt-3.5-turbo'):
#     """使用tiktoken在本地快速计算token数量."""
#     try:
#         encoding = tiktoken.encoding_for_model(model_name)
#     except KeyError:
#         encoding = tiktoken.get_encoding('cl100k_base')  # 使用默认编码

#     token_count = len(encoding.encode(text))
#     return token_count


# 发送请求获取 token 数量（API方式）
# token_start_time = time.time()
# response = client.chat.completions.create(
#     model=os.environ["MODEL_NAME"],
#     messages=[
#         {
#             "role": "user",
#             "content": "Generate a JSON with the brand, model and car_type of the most iconic car from the 90's",
#         }
#     ],
#     temperature=0,
#     max_tokens=1,  # 仅用来统计 token，不让它生成
#     stream=False,
# )
# token_end_time = time.time()

# # 提取 token 计数
# prompt_tokens = response.usage.prompt_tokens
# token_time = token_end_time - token_start_time
# print(
#     f"API Token count: prompt_tokens={prompt_tokens}, execution time={token_time:.4f}s"
# )

# # 本地快速计算token（更快的方式）
# local_token_start_time = time.time()
# message_content = "Generate a JSON with the brand, model and car_type of the most iconic car from the 90's"
# # local_token_count = count_tokens_locally(message_content)
# local_token_end_time = time.time()
# local_token_time = local_token_end_time - local_token_start_time

# print(
#     f'Local Token count: token_count={local_token_count}, execution time={local_token_time:.4f}s'
# )
# print(
#     f'Speed comparison: Local is {token_time / local_token_time:.1f}x faster than API'
# )
