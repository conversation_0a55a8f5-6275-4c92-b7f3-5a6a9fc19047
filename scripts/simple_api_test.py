#!/usr/bin/env python3
"""
简单的API测试脚本
快速测试红线审查API
"""

import json

import httpx
from scripts.api_pem import generate_ms_token


def test_api():
    """简单API测试."""
    # API配置
    api_url = "https://dc-ms.dev.kucoin.net/dc-llms-redline-review/review"
    app_id = "big-data"
    public_key = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCiucD7eIJ8/qqxVUz/N0FSu5YRC/u3OXSjcf84F8SFe23QtZ5sKMpG8ePc7NOZv1t2s9FE2lvSJhkYAvEO5iNwE8yo/dDrCGZX4RZN9O9PXlyPTf3TuXIElTmidR+5Wjz1yEbb7PU5V2Pcrk5G+OKynwSsbGr+YjpQXbrmuthAjQIDAQAB"

    # 生成MS-Token
    ms_token = generate_ms_token(app_id, public_key)

    # 请求头
    headers = {"Content-Type": "application/json", "MS-Token": ms_token}

    # 测试数据（与demo.py一致）
    data = {
        "caseId": "10066130",  # 修改为驼峰命名
        "messages": [
            {"id": 123, "type": "USER", "msg": "I forgot my password"},
            {"id": 124, "type": "AGENT", "msg": "I'll help you with that"},
            {
                "id": 125,
                "type": "AGENT",
                "msg": "How can you be so stupid, can't even remember your password",
            },
            {
                "id": 126,
                "type": "USER",
                "msg": "Can't you speak properly, I'm going to complain about you",
            },
            {"id": 127, "type": "AGENT", "msg": "Fuck you"},
            {"id": 128, "type": "AGENT", "msg": "Fuck you"},
        ],
    }

    print(f"🚀 发送请求到: {api_url}")
    print(f"🔑 MS-Token: {ms_token[:50]}...")

    try:
        # 发送POST请求
        with httpx.Client(verify=False, timeout=60.0) as client:
            response = client.post(api_url, json=data, headers=headers)

        print(f"📈 响应状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ 请求成功!")
            print("\n📋 审查结果:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"❌ 请求失败: {response.text}")

    except httpx.TimeoutException:
        print("⏰ 请求超时")
    except httpx.ConnectError:
        print("🔌 连接失败")
    except Exception as e:
        print(f"❌ 请求异常: {e}")


if __name__ == "__main__":
    test_api()
